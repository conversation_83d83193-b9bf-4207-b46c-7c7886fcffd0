{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/eslint": "1.4.1", "@nuxt/fonts": "0.11.4", "@nuxt/icon": "1.13.0", "@nuxt/image": "1.10.0", "@nuxt/ui": "3.1.2", "@nuxtjs/leaflet": "^1.2.6", "@nuxtjs/supabase": "^1.5.1", "chart.js": "^4.4.9", "d3": "^7.9.0", "d3-drag": "^3.0.0", "d3-force": "^3.0.0", "d3-selection": "^3.0.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.0", "eslint": "^9.0.0", "nuxt": "^3.17.3", "postgres": "^3.4.7", "typescript": "^5.6.3", "vitest": "^3.1.4", "vue": "^3.5.14", "vue-router": "^4.5.1"}, "devDependencies": {"@iconify-json/heroicons": "^1.2.2", "@types/d3": "^7.4.3", "drizzle-kit": "^0.31.1", "tsx": "^4.19.4"}}